<template>
  <div class="quick-filter-badges">
    <div class="filter-group">
      <ul class="badge-list">
        <li
          v-for="filter in statusFilters"
          :key="filter.id"
          class="badge-item"
          :class="{ active: filter.id === activeStatusFilter }"
          @click="selectStatusFilter(filter.id)"
        >
          <span class="filter-name">{{ filter.name }}</span>
          <el-badge :value="filter.count" class="badge-count" />
        </li>
      </ul>
    </div>
    <div class="filter-group">
      <ul class="badge-list">
        <li
          v-for="filter in timeFilters"
          :key="filter.id"
          class="badge-item"
          :class="{ active: filter.id === activeTimeFilter }"
          @click="selectTimeFilter(filter.id)"
        >
          <span class="filter-name">{{ filter.name }}</span>
          <el-badge :value="filter.count" class="badge-count" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElBadge } from 'element-plus'

// Define emits for component communication
const emit = defineEmits(['status-filter-change', 'time-filter-change'])

// Sample data for filters - in a real application, this would likely be passed as props or fetched
const statusFilters = ref([
  { id: 'all', name: '全部', count: 12 },
  { id: 'pending', name: '待审核', count: 2 },
  { id: 'approved', name: '已审核', count: 1 },
  { id: 'fulfilled', name: '已交单', count: 1 },
  { id: 'exported', name: '已导出', count: 1 },
  { id: 'picking', name: '配货中', count: 1 },
  { id: 'shipped', name: '已发货', count: 2 },
  { id: 'completed', name: '已完成', count: 1 },
  { id: 'closed', name: '已关闭', count: 1 },
  { id: 'after_sale', name: '售后中', count: 1 },
])

const timeFilters = ref([
  { id: 'countdown_half_h', name: '倒计半H', count: 1 },
  { id: 'countdown_1h', name: '倒计1H', count: 1 },
  { id: 'countdown_2h', name: '倒计2H', count: 2 },
  { id: 'countdown_4h', name: '倒计4H', count: 3 },
  { id: 'countdown_8h', name: '倒计8H', count: 4 },
  { id: 'overdue', name: '已超时', count: 1 },
  { id: 'virtual_shipping_24h', name: '虚拟发货24H', count: 1 },
])

const activeStatusFilter = ref('all')
const activeTimeFilter = ref('countdown_4h')

const selectStatusFilter = (id: string) => {
  activeStatusFilter.value = id
  emit('status-filter-change', id)
}

const selectTimeFilter = (id: string) => {
  activeTimeFilter.value = id
  emit('time-filter-change', id)
}
</script>

<style scoped lang="scss">
.quick-filter-badges {
  background-color: white;
  padding: 8px 12px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
}

.filter-group:not(:last-child) {
  margin-bottom: 8px;
}

.badge-list {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  // gap: 8px; // Removed gap for tight layout
}

.badge-item {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 32px;
  border: 1px solid var(--el-border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
  color: var(--el-text-color-regular);

  &:not(:last-child) {
    margin-right: -1px; // Overlap borders
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  &:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &:hover {
    z-index: 1;
    border-color: var(--el-color-primary);
  }

  .filter-name {
    font-size: 14px;
  }

  .badge-count {
    margin-left: 8px;
    :deep(.el-badge__content) {
      background-color: var(--el-color-info-light-7);
      color: var(--el-text-color-secondary);
      border: none;
    }
  }
}

.badge-item.active {
  z-index: 2;
  background-color: var(--el-color-primary);
  color: #fff;
  border-color: var(--el-color-primary);

  .badge-count {
    :deep(.el-badge__content) {
      background-color: #fff;
      color: var(--el-color-primary);
    }
  }
}
</style> 