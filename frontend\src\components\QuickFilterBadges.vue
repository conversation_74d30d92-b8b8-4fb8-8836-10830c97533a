<template>
  <div class="quick-filter-badges">
    <!-- 快筛徽章 - 一行显示 -->
    <div class="tabs-vertical-badge">
      <!-- 状态筛选徽章 -->
      <li
        v-for="filter in statusFilters"
        :key="filter.id"
        :class="{ active: filter.id === activeStatusFilter }"
        @click="selectStatusFilter(filter.id)"
      >
        <a href="#" @click.prevent>
          {{ filter.name }}
          <span class="badge">{{ filter.count }}</span>
        </a>
      </li>

      <!-- 时间筛选徽章 -->
      <li
        v-for="filter in timeFilters"
        :key="filter.id"
        :class="{ active: filter.id === activeTimeFilter }"
        @click="selectTimeFilter(filter.id)"
      >
        <a href="#" @click.prevent>
          {{ filter.name }}
          <span class="badge">{{ filter.count }}</span>
        </a>
      </li>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// Define emits for component communication
const emit = defineEmits(['status-filter-change', 'time-filter-change'])

// Sample data for filters - in a real application, this would likely be passed as props or fetched
const statusFilters = ref([
  { id: 'all', name: '全部', count: 12 },
  { id: 'pending', name: '待审核', count: 2 },
  { id: 'approved', name: '已审核', count: 1 },
  { id: 'fulfilled', name: '已交单', count: 1 },
  { id: 'exported', name: '已导出', count: 1 },
  { id: 'picking', name: '配货中', count: 1 },
  { id: 'shipped', name: '已发货', count: 2 },
  { id: 'completed', name: '已完成', count: 1 },
  { id: 'closed', name: '已关闭', count: 1 },
  { id: 'after_sale', name: '售后中', count: 1 },
])

const timeFilters = ref([
  { id: 'countdown_half_h', name: '倒计半H', count: 1 },
  { id: 'countdown_1h', name: '倒计1H', count: 1 },
  { id: 'countdown_2h', name: '倒计2H', count: 2 },
  { id: 'countdown_4h', name: '倒计4H', count: 3 },
  { id: 'countdown_8h', name: '倒计8H', count: 4 },
  { id: 'overdue', name: '已超时', count: 1 },
  { id: 'virtual_shipping_24h', name: '虚拟发货24H', count: 1 },
])

const activeStatusFilter = ref('all')
const activeTimeFilter = ref('countdown_4h')

const selectStatusFilter = (id: string) => {
  activeStatusFilter.value = id
  emit('status-filter-change', id)
}

const selectTimeFilter = (id: string) => {
  activeTimeFilter.value = id
  emit('time-filter-change', id)
}

// 键盘导航功能
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
    event.preventDefault()

    // 获取当前激活的状态筛选器索引
    const currentIndex = statusFilters.value.findIndex(filter => filter.id === activeStatusFilter.value)
    let newIndex = currentIndex

    if (event.key === 'ArrowLeft') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : statusFilters.value.length - 1
    } else if (event.key === 'ArrowRight') {
      newIndex = currentIndex < statusFilters.value.length - 1 ? currentIndex + 1 : 0
    }

    selectStatusFilter(statusFilters.value[newIndex].id)
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 动态更新徽章数量
const updateBadgeCount = (filterType: string, count: number, isTimeFilter = false) => {
  const filters = isTimeFilter ? timeFilters.value : statusFilters.value
  const filter = filters.find(f => f.id === filterType)
  if (filter) {
    filter.count = count
  }
}

// 暴露方法给父组件使用
defineExpose({
  updateBadgeCount
})
</script>

<style scoped>
/* CSS变量定义 - 优先使用主题变量 */
:root {
  /* 主色系 - 引用主题颜色 */
  --primary-color: var(--el-color-primary, #dc2626);
  --primary-hover: var(--el-color-primary-dark-2, #b91c1c);

  /* 文本色系 */
  --text-primary: #374151;
  --text-secondary: #6b7280;
  --text-white: #ffffff;

  /* 背景色系 */
  --bg-white: #ffffff;
  --bg-gray-50: #f9fafb;

  /* 边框色系 */
  --border-color: #e5e7eb;

  /* 圆角系统 */
  --radius-full: 9999px;

  /* 过渡动画 */
  --transition-slow: 0.3s ease;
}

.quick-filter-badges {
  background-color: var(--bg-white);
  padding: 0;
  margin: 0;
}

/* ===== 订单列表快筛徽章样式 ===== */

/* 徽章容器 */
.tabs-vertical-badge {
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
  flex-wrap: wrap;
  gap: 0;
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  z-index: 50;
  height: 40px;
}

/* 徽章项目 */
.tabs-vertical-badge li {
  position: relative;
  margin: 0;
  padding: 0;
}

/* 徽章链接 */
.tabs-vertical-badge li a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  width: 100px;
  height: 40px;
  padding: 0 12px;
  margin: 0;
  color: var(--text-secondary);
  background-color: transparent;
  text-decoration: none;
  transition: all var(--transition-slow);
  border: none;
  border-bottom: 1px solid transparent;
  position: relative;
  font-weight: 500;
  font-size: 13px;
  box-sizing: border-box;
  z-index: 100;
}

/* 底线指示器 */
.tabs-vertical-badge li a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: transparent;
  transition: all var(--transition-slow);
}

/* 数字徽章 */
.tabs-vertical-badge .badge {
  background-color: var(--el-color-primary, #409eff) !important;
  color: var(--text-white) !important;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 5px;
  border-radius: var(--radius-full);
  transition: all var(--transition-slow);
  min-width: 16px;
  text-align: center;
  line-height: 1;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
  z-index: 1000;
}

/* 激活状态和悬停效果 - 使用更强的选择器 */
.tabs-vertical-badge li.active a,
.tabs-vertical-badge li a:hover {
  background-color: var(--bg-gray-50) !important;
  color: var(--el-color-primary, #409eff) !important;
  border-bottom: 1px solid var(--el-color-primary, #409eff) !important;
}

/* 激活状态的底线指示器 - 使用更强的选择器 */
.tabs-vertical-badge li.active a::before,
.tabs-vertical-badge li a:hover::before {
  background-color: var(--el-color-primary, #409eff) !important;
  height: 1px !important;
}

/* 激活/悬停时，徽章样式反转 */
.tabs-vertical-badge li.active .badge,
.tabs-vertical-badge li a:hover .badge {
  background-color: var(--text-white) !important;
  color: var(--el-color-primary, #409eff) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-vertical-badge {
    flex-direction: column;
    gap: 0;
    height: auto;
  }

  .tabs-vertical-badge li {
    margin: 0;
    width: 100%;
  }

  .tabs-vertical-badge li a {
    width: 100%;
    justify-content: flex-start;
    height: 40px;
    padding: 0 16px;
    border: 0;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
  }

  .tabs-vertical-badge li:last-child a {
    border-bottom: 0;
  }
}
</style>